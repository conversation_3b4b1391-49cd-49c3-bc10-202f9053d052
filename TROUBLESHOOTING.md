# 园区车辆信息管理系统 - 问题排查指南

## 常见问题及解决方案

### 1. Maven相关问题

#### 问题：`mvn : 无法将"mvn"项识别为 cmdlet、函数、脚本文件或可运行程序的名称`

**原因**：Maven未安装或未配置到系统PATH中

**解决方案**：
1. 下载并安装Maven：https://maven.apache.org/download.cgi
2. 配置环境变量：
   - 新建系统变量 `MAVEN_HOME`，值为Maven安装目录
   - 在PATH中添加 `%MAVEN_HOME%\bin`
3. 验证安装：打开新的命令行窗口，执行 `mvn -version`

#### 问题：Maven依赖下载失败

**解决方案**：
1. 检查网络连接
2. 配置Maven镜像源（推荐阿里云）：
```xml
<!-- 在Maven的settings.xml中添加 -->
<mirror>
    <id>alimaven</id>
    <name>aliyun maven</name>
    <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
    <mirrorOf>central</mirrorOf>
</mirror>
```

### 2. Spring Boot启动问题

#### 问题：`Failed to configure a DataSource`

**原因**：数据库连接配置错误或数据库未启动

**解决方案**：
1. 确保MySQL服务已启动
2. 检查数据库连接配置：
   - 数据库URL
   - 用户名和密码
   - 数据库是否存在
3. 执行数据库初始化脚本：`sql/init.sql`

#### 问题：`Port 8080 was already in use`

**原因**：端口被占用

**解决方案**：
1. 查找占用端口的进程：`netstat -ano | findstr :8080`
2. 结束占用进程：`taskkill /PID <进程ID> /F`
3. 或修改application.yml中的端口配置

### 3. Eureka相关问题

#### 问题：服务无法注册到Eureka

**解决方案**：
1. 确保Eureka Server已启动（http://localhost:8761）
2. 检查服务配置中的eureka.client.service-url.defaultZone
3. 检查网络连接和防火墙设置

#### 问题：`com.sun.jersey.api.client.ClientHandlerException`

**原因**：Spring Cloud版本兼容性问题

**解决方案**：
已在项目中使用兼容的版本组合：
- Spring Boot: 2.6.13
- Spring Cloud: 2021.0.5

### 4. Feign调用问题

#### 问题：`Load balancer does not have available server`

**原因**：目标服务未注册到Eureka或服务名称错误

**解决方案**：
1. 确保目标服务已启动并注册到Eureka
2. 检查@FeignClient注解中的服务名称
3. 在Eureka控制台确认服务状态

### 5. 前端相关问题

#### 问题：`npm install` 失败

**解决方案**：
1. 清除npm缓存：`npm cache clean --force`
2. 删除node_modules文件夹，重新安装
3. 使用淘宝镜像：`npm install --registry=https://registry.npm.taobao.org`

#### 问题：跨域问题

**原因**：前端直接访问后端服务

**解决方案**：
确保前端通过网关访问后端服务：
- 前端配置：`http://localhost:8080/api`
- 网关路由已配置CORS

### 6. 数据库相关问题

#### 问题：`Access denied for user 'root'@'localhost'`

**解决方案**：
1. 检查MySQL用户名和密码
2. 确保用户有访问数据库的权限
3. 修改application.yml中的数据库配置

#### 问题：`Unknown database 'park_vehicle_db'`

**解决方案**：
执行数据库初始化脚本：
```sql
mysql -u root -p < sql/init.sql
```

## 启动顺序

**重要**：必须按以下顺序启动服务：

1. **启动MySQL数据库**
2. **启动Eureka Server** (端口8761)
   ```bash
   cd eureka-server
   mvn spring-boot:run
   ```
3. **启动业务服务** (可并行启动)
   ```bash
   # 终端1
   cd service-company
   mvn spring-boot:run
   
   # 终端2
   cd service-car
   mvn spring-boot:run
   ```
4. **启动Gateway** (端口8080)
   ```bash
   cd gateway-service
   mvn spring-boot:run
   ```
5. **启动前端** (端口3000)
   ```bash
   cd vue-frontend
   npm run serve
   ```

## 验证服务状态

1. **Eureka控制台**：http://localhost:8761
   - 确认所有服务都已注册

2. **网关健康检查**：http://localhost:8080/actuator/health
   - 如果没有actuator，直接访问路由测试

3. **前端页面**：http://localhost:3000
   - 确认页面正常加载

## 日志查看

如果遇到问题，查看控制台日志：
- 关注ERROR和WARN级别的日志
- 特别注意启动过程中的异常信息
- 检查端口冲突、依赖注入失败等问题

## 开发工具推荐

1. **IDE**：IntelliJ IDEA 或 Eclipse
2. **数据库工具**：Navicat、DBeaver 或 MySQL Workbench
3. **API测试**：Postman 或 Apifox
4. **前端开发**：VS Code

## 联系支持

如果以上解决方案无法解决问题，请提供：
1. 完整的错误日志
2. 系统环境信息（JDK版本、Maven版本等）
3. 具体的操作步骤
