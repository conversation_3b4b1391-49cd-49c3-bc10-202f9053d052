-- 创建数据库
CREATE DATABASE IF NOT EXISTS cardb CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 使用数据库
USE cardb;

-- 创建公司表
CREATE TABLE IF NOT EXISTS company_info (
    cid INT(11) PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    address VARCHAR(255) NOT NULL,
    remark VARCHAR(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建车辆表
CREATE TABLE IF NOT EXISTS car_info (
    id INT(11) PRIMARY KEY AUTO_INCREMENT,
    cartype INT(50) NOT NULL COMMENT '0：轿车，1：货车',
    cid INT(11) NOT NULL,
    begintime DATETIME NOT NULL,
    endtime DATETIME,
    carnum VARCHAR(20) NOT NULL,
    user VARCHAR(20) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    regtype INT NOT NULL COMMENT '0：短期，1：长期',
    FOREIGN KEY (cid) REFERENCES company_info(cid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;