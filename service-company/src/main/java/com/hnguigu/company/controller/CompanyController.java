package com.hnguigu.company.controller;

import com.hnguigu.company.entity.Company;
import com.hnguigu.company.service.CompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/company")
@CrossOrigin
public class CompanyController {
    
    @Autowired
    private CompanyService companyService;

    @GetMapping("/list")
    public List<Company> getAllCompanies() {
        return companyService.getAllCompanies();
    }

    @GetMapping("/{id}")
    public Company getCompanyById(@PathVariable Long id) {
        return companyService.getCompanyById(id);
    }

    @GetMapping("/name/{companyName}")
    public Company getCompanyByName(@PathVariable String companyName) {
        return companyService.getCompanyByName(companyName);
    }
}
