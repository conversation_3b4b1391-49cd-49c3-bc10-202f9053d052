package com.hnguigu.company.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hnguigu.company.entity.Company;
import com.hnguigu.company.mapper.CompanyMapper;
import com.hnguigu.company.service.CompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CompanyServiceImpl implements CompanyService {
    
    @Autowired
    private CompanyMapper companyMapper;

    @Override
    public List<Company> getAllCompanies() {
        return companyMapper.selectList(null);
    }

    @Override
    public Company getCompanyById(Long id) {
        return companyMapper.selectById(id);
    }

    @Override
    public Company getCompanyByName(String companyName) {
        QueryWrapper<Company> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("company_name", companyName);
        return companyMapper.selectOne(queryWrapper);
    }
}
