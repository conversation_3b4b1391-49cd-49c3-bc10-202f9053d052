@echo off
chcp 65001 >nul
echo 启动园区车辆信息管理系统...
echo.

echo 检查Maven是否安装...
mvn -version >nul 2>&1
if errorlevel 1 (
    echo [错误] Maven未安装或未配置到PATH中
    echo 请先安装Maven并配置环境变量
    echo 下载地址: https://maven.apache.org/download.cgi
    pause
    exit /b 1
)

echo 检查Node.js是否安装...
node -v >nul 2>&1
if errorlevel 1 (
    echo [错误] Node.js未安装或未配置到PATH中
    echo 请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo [1/5] 启动Eureka服务注册中心...
start "Eureka Server" cmd /k "cd /d %~dp0eureka-server && mvn spring-boot:run"

echo 等待Eureka启动...
timeout /t 30 /nobreak

echo [2/5] 启动公司服务...
start "Company Service" cmd /k "cd /d %~dp0service-company && mvn spring-boot:run"

echo [3/5] 启动车辆服务...
start "Car Service" cmd /k "cd /d %~dp0service-car && mvn spring-boot:run"

echo 等待微服务启动...
timeout /t 20 /nobreak

echo [4/5] 启动网关服务...
start "Gateway Service" cmd /k "cd /d %~dp0gateway-service && mvn spring-boot:run"

echo 等待网关启动...
timeout /t 15 /nobreak

echo [5/5] 启动前端服务...
start "Vue Frontend" cmd /k "cd /d %~dp0vue-frontend && npm run serve"

echo.
echo ================================
echo 所有服务启动完成！
echo ================================
echo.
echo 访问地址：
echo 前端页面: http://localhost:3000
echo 网关地址: http://localhost:8080
echo Eureka控制台: http://localhost:8761
echo.
echo 如果遇到问题，请查看 TROUBLESHOOTING.md
echo.
pause
