-- 创建数据库
CREATE DATABASE IF NOT EXISTS park_vehicle_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE park_vehicle_db;

-- 创建公司表
CREATE TABLE IF NOT EXISTS company (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    company_name VARCHAR(100) NOT NULL COMMENT '公司名称',
    company_code VARCHAR(50) NOT NULL UNIQUE COMMENT '公司编码',
    contact_person VARCHAR(50) NOT NULL COMMENT '联系人',
    contact_phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    address VARCHAR(200) COMMENT '公司地址',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志(0:未删除,1:已删除)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司信息表';

-- 创建车辆表
CREATE TABLE IF NOT EXISTS car (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    plate_number VARCHAR(20) NOT NULL UNIQUE COMMENT '车牌号',
    owner_name VARCHAR(50) NOT NULL COMMENT '车主姓名',
    owner_phone VARCHAR(20) NOT NULL COMMENT '车主电话',
    company_id BIGINT NOT NULL COMMENT '所属公司ID',
    registration_type VARCHAR(20) NOT NULL COMMENT '登记类型(长期有效/短期有效)',
    expiry_time DATETIME COMMENT '到期时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志(0:未删除,1:已删除)',
    INDEX idx_company_id (company_id),
    INDEX idx_plate_number (plate_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车辆信息表';

-- 插入测试公司数据
INSERT INTO company (company_name, company_code, contact_person, contact_phone, address) VALUES
('阿里巴巴集团', 'ALIBABA001', '张三', '13800138001', '杭州市余杭区文一西路969号'),
('腾讯科技有限公司', 'TENCENT001', '李四', '13800138002', '深圳市南山区科技园'),
('百度在线网络技术公司', 'BAIDU001', '王五', '13800138003', '北京市海淀区上地十街10号'),
('华为技术有限公司', 'HUAWEI001', '赵六', '13800138004', '深圳市龙岗区坂田华为基地'),
('字节跳动科技有限公司', 'BYTEDANCE001', '钱七', '13800138005', '北京市海淀区知春路63号');

-- 插入测试车辆数据
INSERT INTO car (plate_number, owner_name, owner_phone, company_id, registration_type, expiry_time) VALUES
('京A12345', '张小明', '13900139001', 1, '长期有效', NULL),
('粤B67890', '李小红', '13900139002', 2, '短期有效', '2024-12-31 23:59:59'),
('沪C11111', '王小强', '13900139003', 3, '长期有效', NULL),
('深D22222', '赵小美', '13900139004', 4, '短期有效', '2024-11-30 23:59:59'),
('京E33333', '钱小亮', '13900139005', 5, '长期有效', NULL),
('粤F44444', '孙小华', '13900139006', 1, '短期有效', '2024-10-31 23:59:59'),
('沪G55555', '周小丽', '13900139007', 2, '长期有效', NULL),
('深H66666', '吴小刚', '13900139008', 3, '短期有效', '2024-09-30 23:59:59');
