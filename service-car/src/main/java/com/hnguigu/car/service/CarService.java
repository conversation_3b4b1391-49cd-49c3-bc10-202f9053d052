package com.hnguigu.car.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnguigu.car.dto.CarVO;
import com.hnguigu.car.dto.Company;
import com.hnguigu.car.entity.Car;

import java.util.List;

public interface CarService {
    
    /**
     * 分页查询车辆信息
     */
    Page<CarVO> getCarPage(int current, int size, String plateNumber, Long companyId);
    
    /**
     * 获取所有公司信息
     */
    List<Company> getAllCompanies();
    
    /**
     * 添加车辆信息
     */
    boolean addCar(Car car);
    
    /**
     * 更新车辆信息
     */
    boolean updateCar(Car car);
    
    /**
     * 删除车辆信息
     */
    boolean deleteCar(Long id);
    
    /**
     * 根据ID获取车辆信息
     */
    CarVO getCarById(Long id);
}
