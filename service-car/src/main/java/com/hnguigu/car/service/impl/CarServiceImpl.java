package com.hnguigu.car.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnguigu.car.dto.CarVO;
import com.hnguigu.car.dto.Company;
import com.hnguigu.car.entity.Car;
import com.hnguigu.car.feign.CompanyFeignClient;
import com.hnguigu.car.mapper.CarMapper;
import com.hnguigu.car.service.CarService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CarServiceImpl implements CarService {
    
    @Autowired
    private CarMapper carMapper;
    
    @Autowired
    private CompanyFeignClient companyFeignClient;

    @Override
    public Page<CarVO> getCarPage(int current, int size, String plateNumber, Long companyId) {
        // 构建查询条件
        QueryWrapper<Car> queryWrapper = new QueryWrapper<>();
        if (StringUtils.hasText(plateNumber)) {
            queryWrapper.like("plate_number", plateNumber);
        }
        if (companyId != null && companyId > 0) {
            queryWrapper.eq("company_id", companyId);
        }
        
        // 分页查询
        Page<Car> carPage = new Page<>(current, size);
        carMapper.selectPage(carPage, queryWrapper);
        
        // 转换为CarVO并填充公司名称
        Page<CarVO> carVOPage = new Page<>(current, size);
        carVOPage.setTotal(carPage.getTotal());
        carVOPage.setPages(carPage.getPages());
        
        List<CarVO> carVOList = carPage.getRecords().stream().map(car -> {
            CarVO carVO = new CarVO();
            BeanUtils.copyProperties(car, carVO);
            
            // 通过Feign调用获取公司名称
            try {
                Company company = companyFeignClient.getCompanyById(car.getCompanyId());
                if (company != null) {
                    carVO.setCompanyName(company.getCompanyName());
                }
            } catch (Exception e) {
                carVO.setCompanyName("未知公司");
            }
            
            return carVO;
        }).collect(Collectors.toList());
        
        carVOPage.setRecords(carVOList);
        return carVOPage;
    }

    @Override
    public List<Company> getAllCompanies() {
        return companyFeignClient.getAllCompanies();
    }

    @Override
    public boolean addCar(Car car) {
        car.setCreateTime(LocalDateTime.now());
        car.setUpdateTime(LocalDateTime.now());
        return carMapper.insert(car) > 0;
    }

    @Override
    public boolean updateCar(Car car) {
        car.setUpdateTime(LocalDateTime.now());
        return carMapper.updateById(car) > 0;
    }

    @Override
    public boolean deleteCar(Long id) {
        return carMapper.deleteById(id) > 0;
    }

    @Override
    public CarVO getCarById(Long id) {
        Car car = carMapper.selectById(id);
        if (car == null) {
            return null;
        }
        
        CarVO carVO = new CarVO();
        BeanUtils.copyProperties(car, carVO);
        
        // 通过Feign调用获取公司名称
        try {
            Company company = companyFeignClient.getCompanyById(car.getCompanyId());
            if (company != null) {
                carVO.setCompanyName(company.getCompanyName());
            }
        } catch (Exception e) {
            carVO.setCompanyName("未知公司");
        }
        
        return carVO;
    }
}
