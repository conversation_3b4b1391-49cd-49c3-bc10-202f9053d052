package com.hnguigu.car.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CarRegistrationValidatorImpl.class)
@Documented
public @interface CarRegistrationValidator {
    String message() default "登记类型和到期时间不匹配";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
