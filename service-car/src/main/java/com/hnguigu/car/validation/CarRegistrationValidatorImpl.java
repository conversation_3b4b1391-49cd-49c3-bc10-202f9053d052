package com.hnguigu.car.validation;

import com.hnguigu.car.entity.Car;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class CarRegistrationValidatorImpl implements ConstraintValidator<CarRegistrationValidator, Car> {

    @Override
    public void initialize(CarRegistrationValidator constraintAnnotation) {
        // 初始化方法，可以为空
    }

    @Override
    public boolean isValid(Car car, ConstraintValidatorContext context) {
        if (car == null) {
            return true; // 让其他验证器处理null值
        }

        String registrationType = car.getRegistrationType();
        
        if ("长期有效".equals(registrationType)) {
            // 长期有效时，到期时间必须为空
            if (car.getExpiryTime() != null) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("长期有效时不能设置到期时间")
                       .addPropertyNode("expiryTime")
                       .addConstraintViolation();
                return false;
            }
        } else if ("短期有效".equals(registrationType)) {
            // 短期有效时，到期时间必须不为空
            if (car.getExpiryTime() == null) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("短期有效时必须设置到期时间")
                       .addPropertyNode("expiryTime")
                       .addConstraintViolation();
                return false;
            }
        }

        return true;
    }
}
