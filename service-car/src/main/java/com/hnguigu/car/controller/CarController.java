package com.hnguigu.car.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnguigu.car.dto.CarVO;
import com.hnguigu.car.dto.Company;
import com.hnguigu.car.entity.Car;
import com.hnguigu.car.service.CarService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/car")
@CrossOrigin
@Validated
public class CarController {
    
    @Autowired
    private CarService carService;

    /**
     * 分页查询车辆信息
     */
    @GetMapping("/page")
    public Map<String, Object> getCarPage(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String plateNumber,
            @RequestParam(required = false) Long companyId) {
        
        Page<CarVO> page = carService.getCarPage(current, size, plateNumber, companyId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "查询成功");
        result.put("data", page);
        
        return result;
    }

    /**
     * 获取所有公司信息
     */
    @GetMapping("/companies")
    public Map<String, Object> getAllCompanies() {
        List<Company> companies = carService.getAllCompanies();
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "查询成功");
        result.put("data", companies);
        
        return result;
    }

    /**
     * 添加车辆信息
     */
    @PostMapping("/add")
    public Map<String, Object> addCar(@Valid @RequestBody Car car) {
        boolean success = carService.addCar(car);
        
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "添加成功");
        } else {
            result.put("code", 500);
            result.put("message", "添加失败");
        }
        
        return result;
    }

    /**
     * 更新车辆信息
     */
    @PutMapping("/update")
    public Map<String, Object> updateCar(@Valid @RequestBody Car car) {
        boolean success = carService.updateCar(car);
        
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "更新成功");
        } else {
            result.put("code", 500);
            result.put("message", "更新失败");
        }
        
        return result;
    }

    /**
     * 删除车辆信息
     */
    @DeleteMapping("/delete/{id}")
    public Map<String, Object> deleteCar(@PathVariable Long id) {
        boolean success = carService.deleteCar(id);
        
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "删除成功");
        } else {
            result.put("code", 500);
            result.put("message", "删除失败");
        }
        
        return result;
    }

    /**
     * 根据ID获取车辆信息
     */
    @GetMapping("/{id}")
    public Map<String, Object> getCarById(@PathVariable Long id) {
        CarVO carVO = carService.getCarById(id);
        
        Map<String, Object> result = new HashMap<>();
        if (carVO != null) {
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", carVO);
        } else {
            result.put("code", 404);
            result.put("message", "车辆信息不存在");
        }
        
        return result;
    }
}
