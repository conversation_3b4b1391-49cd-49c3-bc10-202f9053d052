package com.hnguigu.car.feign;

import com.hnguigu.car.dto.Company;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

@FeignClient(name = "service-company")
public interface CompanyFeignClient {
    
    @GetMapping("/company/list")
    List<Company> getAllCompanies();
    
    @GetMapping("/company/{id}")
    Company getCompanyById(@PathVariable("id") Long id);
    
    @GetMapping("/company/name/{companyName}")
    Company getCompanyByName(@PathVariable("companyName") String companyName);
}
