# 园区车辆信息管理系统

## 项目简介

本项目是一个基于微服务架构的园区车辆信息管理系统，采用前后端分离的开发模式。

## 技术栈

### 后端技术栈
- **框架**: SpringBoot 2.6.13
- **微服务**: Spring Cloud 2021.0.5
- **服务注册与发现**: Eureka
- **服务调用**: OpenFeign
- **网关**: Spring Cloud Gateway
- **数据库**: MySQL 8.0
- **ORM**: MyBatis-Plus 3.5.2
- **构建工具**: Maven

### 前端技术栈
- **框架**: Vue 2.6.14
- **UI组件库**: Element UI 2.15.13
- **HTTP客户端**: Axios
- **路由**: Vue Router
- **构建工具**: Vue CLI

## 项目结构

```
spring-kaoshi/
├── eureka-server/          # 服务注册中心
├── gateway-service/        # 网关服务
├── service-company/        # 公司信息服务
├── service-car/           # 车辆信息服务
├── vue-frontend/          # Vue前端项目
├── sql/                   # 数据库脚本
├── start-services.bat     # 启动脚本
└── README.md             # 项目说明
```

## 功能特性

### 1. 表格数据展示，分页，条件查询功能
- 支持按车牌号模糊查询
- 支持按公司名称筛选
- 分页显示车辆信息
- 公司名称通过Feign调用service-company服务获取

### 2. 添加功能
- 登记类型分为：长期有效、短期有效
- 长期有效：到期时间不能填写
- 短期有效：到期时间必填
- 其他字段均为必填项

### 3. 编辑功能
- 支持修改车辆信息
- 验证规则与添加功能一致
- 公司名称下拉框通过调用service-company服务动态加载

### 4. 删除功能
- 删除前有确认提示框
- 支持逻辑删除

## 环境要求

- JDK 8+
- Maven 3.6+
- MySQL 8.0+
- Node.js 14+
- npm 6+

## 安装部署

### 1. 环境检查
运行环境检查脚本：
```bash
check-environment.bat
```
确保所有环境都正确配置。

### 2. 数据库准备
```sql
-- 执行sql/init.sql脚本创建数据库和表
mysql -u root -p < sql/init.sql
```

### 3. 修改数据库配置
修改以下文件中的数据库连接信息：
- `service-company/src/main/resources/application.yml`
- `service-car/src/main/resources/application.yml`

### 4. 启动服务

#### 方式一：一键启动（推荐）
```bash
start-services.bat
```

#### 方式二：分步启动（推荐用于调试）
```bash
# 1. 启动Eureka服务注册中心
start-eureka.bat

# 2. 启动公司服务
start-company.bat

# 3. 启动车辆服务
start-car.bat

# 4. 启动网关服务
start-gateway.bat

# 5. 启动前端服务
start-frontend.bat
```

#### 方式三：手动启动
```bash
# 1. 启动Eureka服务注册中心
cd eureka-server
mvn spring-boot:run

# 2. 启动公司服务
cd service-company
mvn spring-boot:run

# 3. 启动车辆服务
cd service-car
mvn spring-boot:run

# 4. 启动网关服务
cd gateway-service
mvn spring-boot:run

# 5. 启动前端服务
cd vue-frontend
npm install
npm run serve
```

## 访问地址

- **前端页面**: http://localhost:3000
- **网关地址**: http://localhost:8080
- **Eureka控制台**: http://localhost:8761

## 服务端口

| 服务名称 | 端口 |
|---------|------|
| eureka-server | 8761 |
| service-company | 8081 |
| service-car | 8082 |
| gateway-service | 8080 |
| vue-frontend | 3000 |

## API接口

### 车辆服务接口
- `GET /api/car/page` - 分页查询车辆信息
- `GET /api/car/companies` - 获取所有公司信息
- `POST /api/car/add` - 添加车辆信息
- `PUT /api/car/update` - 更新车辆信息
- `DELETE /api/car/delete/{id}` - 删除车辆信息
- `GET /api/car/{id}` - 根据ID获取车辆信息

### 公司服务接口
- `GET /api/company/list` - 获取所有公司信息
- `GET /api/company/{id}` - 根据ID获取公司信息
- `GET /api/company/name/{companyName}` - 根据名称获取公司信息

## 注意事项

1. 确保MySQL服务已启动
2. 确保各服务按顺序启动（Eureka -> 业务服务 -> 网关 -> 前端）
3. 前端通过网关访问后端服务，不直接调用业务服务
4. 车辆信息中的公司名称通过Feign调用获取，不使用数据库连表查询

## 开发说明

本项目严格按照微服务架构设计，模拟分库分表场景：
- 车辆信息和公司信息分别存储在不同的服务中
- 通过Feign进行跨服务调用
- 前端统一通过网关访问后端服务
