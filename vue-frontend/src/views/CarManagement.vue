<template>
  <div class="car-management">
    <el-container>
      <el-main>
        <!-- 查询条件 -->
        <el-card class="search-card">
          <div slot="header">
            <span>查询条件</span>
          </div>
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item label="车牌号">
              <el-input v-model="searchForm.plateNumber" placeholder="请输入车牌号" clearable></el-input>
            </el-form-item>
            <el-form-item label="公司名称">
              <el-select v-model="searchForm.companyId" placeholder="请选择公司" clearable>
                <el-option
                  v-for="company in companies"
                  :key="company.id"
                  :label="company.companyName"
                  :value="company.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchCars">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
              <el-button type="success" @click="showAddDialog">添加车辆</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 车辆列表 -->
        <el-card class="table-card">
          <div slot="header">
            <span>车辆信息列表</span>
          </div>
          <el-table :data="carList" border style="width: 100%">
            <el-table-column prop="plateNumber" label="车牌号" width="120"></el-table-column>
            <el-table-column prop="ownerName" label="车主姓名" width="100"></el-table-column>
            <el-table-column prop="ownerPhone" label="车主电话" width="130"></el-table-column>
            <el-table-column prop="companyName" label="公司名称" width="200"></el-table-column>
            <el-table-column prop="registrationType" label="登记类型" width="100"></el-table-column>
            <el-table-column prop="expiryTime" label="到期时间" width="180">
              <template slot-scope="scope">
                {{ scope.row.expiryTime || '长期有效' }}
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180"></el-table-column>
            <el-table-column label="操作" width="150">
              <template slot-scope="scope">
                <el-button size="mini" @click="showEditDialog(scope.row)">编辑</el-button>
                <el-button size="mini" type="danger" @click="deleteCar(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            style="margin-top: 20px; text-align: right;">
          </el-pagination>
        </el-card>

        <!-- 添加/编辑对话框 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
          <el-form :model="carForm" :rules="carRules" ref="carForm" label-width="100px">
            <el-form-item label="车牌号" prop="plateNumber">
              <el-input v-model="carForm.plateNumber" placeholder="请输入车牌号"></el-input>
            </el-form-item>
            <el-form-item label="车主姓名" prop="ownerName">
              <el-input v-model="carForm.ownerName" placeholder="请输入车主姓名"></el-input>
            </el-form-item>
            <el-form-item label="车主电话" prop="ownerPhone">
              <el-input v-model="carForm.ownerPhone" placeholder="请输入车主电话"></el-input>
            </el-form-item>
            <el-form-item label="公司名称" prop="companyId">
              <el-select v-model="carForm.companyId" placeholder="请选择公司" style="width: 100%">
                <el-option
                  v-for="company in companies"
                  :key="company.id"
                  :label="company.companyName"
                  :value="company.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="登记类型" prop="registrationType">
              <el-radio-group v-model="carForm.registrationType" @change="onRegistrationTypeChange">
                <el-radio label="长期有效">长期有效</el-radio>
                <el-radio label="短期有效">短期有效</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="到期时间" prop="expiryTime" v-if="carForm.registrationType === '短期有效'">
              <el-date-picker
                v-model="carForm.expiryTime"
                type="datetime"
                placeholder="请选择到期时间"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitForm">确定</el-button>
          </div>
        </el-dialog>
      </el-main>
    </el-container>
  </div>
</template>

<script>
export default {
  name: 'CarManagement',
  data() {
    return {
      // 查询表单
      searchForm: {
        plateNumber: '',
        companyId: null
      },
      // 车辆列表
      carList: [],
      // 公司列表
      companies: [],
      // 分页信息
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },
      // 对话框
      dialogVisible: false,
      dialogTitle: '添加车辆',
      isEdit: false,
      // 车辆表单
      carForm: {
        id: null,
        plateNumber: '',
        ownerName: '',
        ownerPhone: '',
        companyId: null,
        registrationType: '长期有效',
        expiryTime: null
      },
      // 表单验证规则
      carRules: {
        plateNumber: [
          { required: true, message: '请输入车牌号', trigger: 'blur' }
        ],
        ownerName: [
          { required: true, message: '请输入车主姓名', trigger: 'blur' }
        ],
        ownerPhone: [
          { required: true, message: '请输入车主电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        companyId: [
          { required: true, message: '请选择公司', trigger: 'change' }
        ],
        registrationType: [
          { required: true, message: '请选择登记类型', trigger: 'change' }
        ],
        expiryTime: [
          { required: true, message: '请选择到期时间', trigger: 'change' }
        ]
      }
    }
  },
  mounted() {
    this.loadCompanies()
    this.loadCarList()
  },
  methods: {
    // 加载公司列表
    async loadCompanies() {
      try {
        const response = await this.$http.get('/car/companies')
        if (response.data.code === 200) {
          this.companies = response.data.data
        }
      } catch (error) {
        this.$message.error('加载公司列表失败')
      }
    },
    // 加载车辆列表
    async loadCarList() {
      try {
        const params = {
          current: this.pagination.current,
          size: this.pagination.size,
          plateNumber: this.searchForm.plateNumber,
          companyId: this.searchForm.companyId
        }
        const response = await this.$http.get('/car/page', { params })
        if (response.data.code === 200) {
          const pageData = response.data.data
          this.carList = pageData.records
          this.pagination.total = pageData.total
        }
      } catch (error) {
        this.$message.error('加载车辆列表失败')
      }
    },
    // 查询车辆
    searchCars() {
      this.pagination.current = 1
      this.loadCarList()
    },
    // 重置查询
    resetSearch() {
      this.searchForm = {
        plateNumber: '',
        companyId: null
      }
      this.pagination.current = 1
      this.loadCarList()
    },
    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.size = val
      this.pagination.current = 1
      this.loadCarList()
    },
    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.current = val
      this.loadCarList()
    },
    // 显示添加对话框
    showAddDialog() {
      this.dialogTitle = '添加车辆'
      this.isEdit = false
      this.dialogVisible = true
      this.resetForm()
    },
    // 显示编辑对话框
    showEditDialog(row) {
      this.dialogTitle = '编辑车辆'
      this.isEdit = true
      this.dialogVisible = true
      this.carForm = {
        id: row.id,
        plateNumber: row.plateNumber,
        ownerName: row.ownerName,
        ownerPhone: row.ownerPhone,
        companyId: row.companyId,
        registrationType: row.registrationType,
        expiryTime: row.expiryTime
      }
    },
    // 重置表单
    resetForm() {
      this.carForm = {
        id: null,
        plateNumber: '',
        ownerName: '',
        ownerPhone: '',
        companyId: null,
        registrationType: '长期有效',
        expiryTime: null
      }
      if (this.$refs.carForm) {
        this.$refs.carForm.resetFields()
      }
    },
    // 登记类型改变
    onRegistrationTypeChange(value) {
      if (value === '长期有效') {
        this.carForm.expiryTime = null
      }
    },
    // 提交表单
    submitForm() {
      this.$refs.carForm.validate(async (valid) => {
        if (valid) {
          // 验证短期有效必须填写到期时间
          if (this.carForm.registrationType === '短期有效' && !this.carForm.expiryTime) {
            this.$message.error('短期有效必须填写到期时间')
            return
          }
          // 验证长期有效不能填写到期时间
          if (this.carForm.registrationType === '长期有效') {
            this.carForm.expiryTime = null
          }

          try {
            let response
            if (this.isEdit) {
              response = await this.$http.put('/car/update', this.carForm)
            } else {
              response = await this.$http.post('/car/add', this.carForm)
            }

            if (response.data.code === 200) {
              this.$message.success(response.data.message)
              this.dialogVisible = false
              this.loadCarList()
            } else {
              this.$message.error(response.data.message)
            }
          } catch (error) {
            this.$message.error('操作失败')
          }
        }
      })
    },
    // 删除车辆
    deleteCar(row) {
      this.$confirm('此操作将永久删除该车辆信息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await this.$http.delete(`/car/delete/${row.id}`)
          if (response.data.code === 200) {
            this.$message.success('删除成功')
            this.loadCarList()
          } else {
            this.$message.error(response.data.message)
          }
        } catch (error) {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    }
  }
}
</script>

<style scoped>
.car-management {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form .el-form-item {
  margin-bottom: 10px;
}

.table-card {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
